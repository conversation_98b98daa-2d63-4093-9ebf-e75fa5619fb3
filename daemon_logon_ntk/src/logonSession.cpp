#include "logonSession.h"

int main(int argc, char* argv[])
{
    int ret;
    int hOpenSocket;
    int clisock = 0 ;
    int nPort;
    socklen_t clilen=0;
    struct sockaddr_in cli_addr;
    struct timeval outtime;
    fd_set rset;
    char szIP[16];
    char logMsg[1024];
  
	memset(logMsg	,0x00	,sizeof(logMsg));
	memset(szIP		,0x00	,sizeof(szIP));


    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }

    ret = configParse(argv[1]);
    if( ret != 0 )
    {
        ml_sub_end();
        exit(1);
    }

    printf("ServerIP[%s]logonDBName[%s]serverPORT[%d]process_sleep_time[%d]bindir[%s]cfgdir[%s]logPath[%s]domainPath[%s]\n"
       	,gConf.serverIP
		,gConf.logonDBName
		,gConf.serverPORT
		,gConf.process_sleep_time
		,gConf.bindir
		,gConf.cfgdir
		,gConf.logPath
		,gConf.domainPath
		);
    sprintf(logMsg,"[INF] config\n- ServerIP[%s]\n- logonDBName[%s]\n- serverPORT[%d]\n- process_sleep_time[%d]\n- bindir[%s]\n- cfgdir[%s]\n- logPath[%s]\n- domainPath[%s]\n"
       	,gConf.serverIP
		,gConf.logonDBName
		,gConf.serverPORT
		,gConf.process_sleep_time
		,gConf.bindir
		,gConf.cfgdir
		,gConf.logPath
		,gConf.domainPath
		);
    monitoring(logMsg,0,0);

    sprintf(logMsg,"[INF] start [%s]",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server_Fork();


    strcpy(szIP,gConf.serverIP);
    nPort = gConf.serverPORT;

    /* socket open */
    ret=UnixSockOpenNon(&hOpenSocket,szIP,nPort);
    if ( ret!=0 ) 
	{
        monitoring("[ERR] socket - open",0,errno);
        ml_sub_end();
        if ( hOpenSocket )
		{
			close( hOpenSocket );
		} 
		return -1;
    }

    while(activeProcess)
    {
        wait_a_moment(gConf.process_sleep_time);
        outtime.tv_sec = 0;
        outtime.tv_usec = 50000;
        FD_ZERO(&rset);
        FD_SET(hOpenSocket,&rset);
        ret = select(hOpenSocket+1, (fd_set*)&rset, (fd_set*)NULL, (fd_set*)NULL, &outtime);
        if ( ret>0 ) 
		{
            getAccept(hOpenSocket,szIP,nPort);
        }
    }
    sprintf(logMsg,"[INF] close [%s]",PROCESS_NAME);
    
	if(hOpenSocket) 
	{
		close(hOpenSocket);
	}

    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}

int getAccept(int hOpenSocket,char* szIP,int nPort)
{
    int hNewSocket = 0 ; /** new connection socket */
    int ret;
    socklen_t clilen=0; /** new connection socket size */
    struct sockaddr_in cli_addr; /** new connection info struct */
    struct timeval outtime;
    fd_set rset;
    pid_t childPid;
    char szKillCmd[32];

    clilen = sizeof(cli_addr);
    hNewSocket = accept(hOpenSocket, (struct sockaddr*)&cli_addr, &clilen);
    
	if( hNewSocket < 0 )   
	{
		/* log_history -> monitoring :: file : lib/command_util.cpp */
        log_history(0,0,"[ERR] socket accept client",strerror(errno));
        return -1;
    }
    else if (hNewSocket > 0) 
	{
        if ( strstr(gConf.l4IP, inet_ntoa(cli_addr.sin_addr)) != NULL)
        {
        	/* L4 스위치 폴링 SKIP */
            close(hNewSocket);
            return -1;
        }
        
        log_history(0,0,"[INF] socket connection - client ip [%s][%s:%d]", inet_ntoa(cli_addr.sin_addr), __FILE__, __LINE__);
        /** check logon */
        // check logon function
        CLogonDbInfo logonDbInfo;
        memset(&logonDbInfo, 0x00, sizeof(logonDbInfo));
        ret = requestLogon(hNewSocket,logonDbInfo);
		if( ret < 0 ) 
		{ 
			log_history(0,0,"[ERR] requestLogon failed",strerror(errno));
			close(hNewSocket);
			return -1;
        }

        strcpy(logonDbInfo.szIP			,inet_ntoa(cli_addr.sin_addr));
        strcpy(logonDbInfo.szLogPath	,gConf.logPath);
        strcpy(logonDbInfo.szDomainPath	,gConf.domainPath);

        ret = checkServerInfo(logonDbInfo,szIP,nPort);
		if( ret != 0 ) 
		{
			log_history(0,0,"[ERR] checkServerInfo failed",strerror(errno));
			close(hNewSocket);
			return -1;
		}

        /* 중복 처리 : 기존 접속을 끊는다. */
        /*
        CCL(szKillCmd);
        if( logonDbInfo.classify == 'S' )
            sprintf(szKillCmd,"adm -s %s",logonDbInfo.szCID);
        else
            sprintf(szKillCmd,"adm -r %s",logonDbInfo.szCID);

        system(szKillCmd);
        */


		////////////////////////////////////////////////////////////////////////////////////

		//		log_history(0,0,"REPORT 여부:%c 일제한건수:%d 일누적건수:%d  월제한건수:%d 월누적건수: %d",logonDbInfo.classify, logonDbInfo.nDayLimitCnt,logonDbInfo.nDayAccCnt,logonDbInfo.nMonLimitCnt,  logonDbInfo.nMonAccCnt+logonDbInfo.nDayAccCnt );

		if (logonDbInfo.classify == 'N' || logonDbInfo.classify == 'S')
		//SenderProcess 일때만 제한 처리 한다.  (logonDbInfo - logonDBInfo.h 각 클래스 변수 추가 수정)
		{
			if (atoi(logonDbInfo.szLimitFlag) != 3) {
				switch (atoi(logonDbInfo.szLimitType)) {
					case 0: // 제한 적용 안함
						break;
					case 1: // 일,월 적용
						if (logonDbInfo.nDayLimitCnt < logonDbInfo.nDayAccCnt) //일 누적 카운트가 더 큰 경우 종료
						{
							log_history(0, 0, "[ERR] limit excess daily - connect end [%s] day limit [%d]",
										logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
							close(hNewSocket);
							return -1;
						}
						if (logonDbInfo.nMonLimitCnt < logonDbInfo.nMonAccCnt + logonDbInfo.nDayAccCnt)
						//월 누적 카운트가 더 큰 경우 종료
						//		if (logonDbInfo.nMonLimitCnt < logonDbInfo.nMonAccCnt)	//월 누적 카운트가 더 큰 경우 종료
						{
							log_history(0, 0, "[ERR] limit excess monthly - connect end [%s] mon limit [%d]",
										logonDbInfo.szCID, logonDbInfo.nMonAccCnt);
							close(hNewSocket);
							return -1;
						}
						break;
					case 2: // 일 적용
						if (logonDbInfo.nDayLimitCnt < logonDbInfo.nDayAccCnt) //일 누적 카운트가 더 큰 경우 종료
						{
							log_history(0, 0, "[ERR] limit excess daily - connect end [%s] day limit [%d]",
										logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
							close(hNewSocket);
							return -1;
						}
						break;
					case 3: // 월 적용
						if (logonDbInfo.nMonLimitCnt < logonDbInfo.nMonAccCnt + logonDbInfo.nDayAccCnt)
						//월 누적 카운트가 더 큰 경우 종료
						{
							log_history(0, 0, "[ERR] limit excess monthly - connect end [%s] mon limit [%d]",
										logonDbInfo.szCID, logonDbInfo.nMonAccCnt);
							close(hNewSocket);
							return -1;
						}
						break;
					default:
						break;
				}
			}
		}
        
		//////////////////////////////////////////////////////////////////////
	
	    // Create Pipe
        log_history(0,0,"[INF] Creating pipe for CID[%s]", logonDbInfo.szCID);
        int fd[2];
        if( pipe(fd) == -1 )
        {
            log_history(0,0,"[ERR] pipe creation failed [%s] errno[%d]",strerror(errno), errno);
            close(hNewSocket);
            return -1;
        }
        log_history(0,0,"[INF] Pipe created successfully - read_fd[%d] write_fd[%d]", fd[0], fd[1]);

        log_history(0,0,"[INF] Forking child process for CID[%s] classify[%c]", logonDbInfo.szCID, logonDbInfo.classify);
        childPid = fork();// Create Child Process

        if( childPid < 0 )
        { // Error
            log_history(0,0,"[ERR] fork failed [%s] errno[%d]",strerror(errno), errno);
            close(fd[0]);
            close(fd[1]);
            close(hNewSocket);
            return -2;
        }

        if( childPid == 0 )
        { /** child Process */
            log_history(0,0,"[INF] Child process started - PID[%d] CID[%s] classify[%c]", getpid(), logonDbInfo.szCID, logonDbInfo.classify);

            char szProcessName[32];
            char szSockfd[8];
            char szPipefd[8];
            //char szCfgDir[64];
        	char szCfgDir[256];

            close(hOpenSocket);

            close(fd[1]);

            CCL(szCfgDir);
            sprintf(szSockfd,"%d",hNewSocket);
            sprintf(szPipefd,"%d",fd[0]);

            log_history(0,0,"[INF] Child process setup - sockfd_str[%s] pipefd_str[%s] socket[%d] pipe_read[%d]",
                        szSockfd, szPipefd, hNewSocket, fd[0]);

            switch( logonDbInfo.classify )
            {
                case 'N':
                case 'S':
                    /*
                     * 1 : sockfd
                     * 2 : pipe
                     * 3 : version
                     * 4 : conf file
                     */
                    // 실행 전 상세 정보 로깅
                    log_history(0,0,"[INF] Before execlp sender - CID[%s] PID[%d] sockfd[%s] pipefd[%s]",
                                logonDbInfo.szCID, getpid(), szSockfd, szPipefd);

                    printf("========test S");
                    //sprintf(szCfgDir,"%s/%s.conf",gConf.cfgdir,logonDbInfo.szSenderName);
            		snprintf(szCfgDir, sizeof(szCfgDir),
            			"%s/%s.conf",gConf.cfgdir,logonDbInfo.szSenderName);
                    snprintf(szProcessName, sizeof(szProcessName), "ID_S_%s",logonDbInfo.szCID);

                    log_history(0,0,"[INF] Sender exec info:\n- senderName[%s]\n- processName[%s]\n- bindir[%s]\n- cfgdir[%s]\n- cfgfile[%s]",
                                logonDbInfo.szSenderName, szProcessName, gConf.bindir, gConf.cfgdir, szCfgDir);

                    // 파일 존재 여부 확인
                    char fullPath[512];
                    snprintf(fullPath, sizeof(fullPath), "%s/%s", gConf.bindir, logonDbInfo.szSenderName);
                    if (access(fullPath, X_OK) != 0) {
                        log_history(0,0,"[ERR] Sender executable not found or not executable: [%s] errno[%d](%s)",
                                    fullPath, errno, strerror(errno));
                        exit(1);
                    }

                    // 설정 파일 존재 여부 확인
                    if (access(szCfgDir, R_OK) != 0) {
                        log_history(0,0,"[ERR] Config file not found or not readable: [%s] errno[%d](%s)",
                                    szCfgDir, errno, strerror(errno));
                        exit(1);
                    }

                    log_history(0,0,"[INF] Files verified - executable[%s] config[%s]", fullPath, szCfgDir);

                    // chdir 전 현재 디렉토리 로깅
                    char currentDir[256];
                    if (getcwd(currentDir, sizeof(currentDir)) != NULL) {
                        log_history(0,0,"[INF] Current directory before chdir: [%s]", currentDir);
                    }

                    if (chdir(gConf.bindir) < 0) {
                        log_history(0,0,"[ERR] chdir failed: [%s] errno[%d](%s)",
                                    gConf.bindir, errno, strerror(errno));
                        exit(1);
                    }

                    log_history(0,0,"[INF] Changed directory to: [%s]", gConf.bindir);

                    // execlp 직전 로깅
                    log_history(0,0,"[INF] Executing: execlp(%s, %s, %s, %s, %s, %s)",
                                logonDbInfo.szSenderName, szProcessName, szSockfd, szPipefd, "*******", szCfgDir);

                    // 실행 직전에 flush하여 로그가 확실히 기록되도록
                    fflush(stdout);
                    fflush(stderr);

                    ret = execlp(logonDbInfo.szSenderName,
                            szProcessName,
                            szSockfd,
                            szPipefd,
                            "*******",
                            szCfgDir,
														(char *)0);

                    // execlp가 실패한 경우에만 여기에 도달
                    log_history(0,0,"[ERR] execlp sender FAILED\n- errno[%d](%s)\n- senderName[%s]\n- szProcessName[%s]\n- CfgDir[%s]\n- sockfd[%s]\n- pipefd[%s]",
                                errno, strerror(errno),
                                logonDbInfo.szSenderName,
                                szProcessName,
                                szCfgDir,
                                szSockfd,
                                szPipefd);

                    // 추가 디버깅 정보
                    log_history(0,0,"[ERR] Additional debug info:\n- UID[%d] GID[%d]\n- PATH[%s]",
                                getuid(), getgid(), getenv("PATH") ? getenv("PATH") : "NULL");

                    exit(1);  // execlp 실패 시 명시적으로 exit

                    break;
                case 'Y':
                case 'R':
                    // 실행 전 상세 정보 로깅
                    log_history(0,0,"[INF] Before execlp report - CID[%s] PID[%d] sockfd[%s] pipefd[%s]",
                                logonDbInfo.szCID, getpid(), szSockfd, szPipefd);

                    // report
                    printf("========test R");
                    //sprintf(szCfgDir,"%s/%s.conf",gConf.cfgdir,logonDbInfo.szReportName);
            		snprintf(szCfgDir, sizeof(szCfgDir),
            			"%s/%s.conf",gConf.cfgdir,logonDbInfo.szReportName);
                    snprintf(szProcessName, sizeof(szProcessName), "ID_R_%s",logonDbInfo.szCID);

                    log_history(0,0,"[INF] Report exec info:\n- reportName[%s]\n- processName[%s]\n- bindir[%s]\n- cfgdir[%s]\n- cfgfile[%s]",
                                logonDbInfo.szReportName, szProcessName, gConf.bindir, gConf.cfgdir, szCfgDir);

                    // 파일 존재 여부 확인
                    char fullPathR[512];
                    snprintf(fullPathR, sizeof(fullPathR), "%s/%s", gConf.bindir, logonDbInfo.szReportName);
                    if (access(fullPathR, X_OK) != 0) {
                        log_history(0,0,"[ERR] Report executable not found or not executable: [%s] errno[%d](%s)",
                                    fullPathR, errno, strerror(errno));
                        exit(1);
                    }

                    // 설정 파일 존재 여부 확인
                    if (access(szCfgDir, R_OK) != 0) {
                        log_history(0,0,"[ERR] Config file not found or not readable: [%s] errno[%d](%s)",
                                    szCfgDir, errno, strerror(errno));
                        exit(1);
                    }

                    log_history(0,0,"[INF] Files verified - executable[%s] config[%s]", fullPathR, szCfgDir);

                    if (chdir(gConf.bindir) < 0) {
                        log_history(0,0,"[ERR] chdir failed: [%s] errno[%d](%s)",
                                    gConf.bindir, errno, strerror(errno));
                        exit(1);
                    }

                    log_history(0,0,"[INF] Changed directory to: [%s]", gConf.bindir);

                    // execlp 직전 로깅
                    log_history(0,0,"[INF] Executing: execlp(%s, %s, %s, %s, %s, %s)",
                                logonDbInfo.szReportName, szProcessName, szSockfd, szPipefd, "*******", szCfgDir);

                    ret = execlp(logonDbInfo.szReportName,
                            szProcessName,
                            szSockfd,
                            szPipefd,
                            "*******",
                            szCfgDir,(char *)0);

                    // execlp가 실패한 경우에만 여기에 도달
                    log_history(0,0,"[ERR] execlp report FAILED\n- errno[%d](%s)\n- reportName[%s]\n- szProcessName[%s]\n- CfgDir[%s]\n- sockfd[%s]\n- pipefd[%s]",
                                errno, strerror(errno),
                                logonDbInfo.szReportName,
                                szProcessName,
                                szCfgDir,
                                szSockfd,
                                szPipefd);

                    // 추가 디버깅 정보
                    log_history(0,0,"[ERR] Additional debug info:\n- UID[%d] GID[%d]\n- PATH[%s]",
                                getuid(), getgid(), getenv("PATH") ? getenv("PATH") : "NULL");

                    exit(1);  // execlp 실패 시 명시적으로 exit

//                    reportProcess(hNewSocket,logonDbInfo);
                    break;
                default:
                    log_history(0,0,"[ERR] process type invalid 'Y/N,S/R' - [%c]",logonDbInfo.classify );
                    break;
            }

            exit(0);
        }

        // parent Process
        //
        log_history(0,0,"[INF] Parent process - child PID[%d] created for CID[%s]", childPid, logonDbInfo.szCID);

        // pipe를 통한 데이터 전송
        ssize_t written = write(fd[1], (char*)&logonDbInfo, sizeof(logonDbInfo));
        if (written != sizeof(logonDbInfo)) {
            log_history(0,0,"[ERR] Pipe write failed - expected[%zu] written[%zd] errno[%d](%s)",
                        sizeof(logonDbInfo), written, errno, strerror(errno));
        } else {
            log_history(0,0,"[INF] Pipe write success - written[%zd] bytes to child PID[%d]", written, childPid);
        }

        close(fd[0]);
        close(fd[1]);
        close(hNewSocket);

        // 자식 프로세스가 정상적으로 시작되었는지 확인
        sleep(1);  // 잠시 대기
        if (kill(childPid, 0) == 0) {
            log_history(0,0,"[INF] Child process [%d] is running for CID[%s]", childPid, logonDbInfo.szCID);
        } else {
            log_history(0,0,"[ERR] Child process [%d] is not running for CID[%s] errno[%d](%s)",
                        childPid, logonDbInfo.szCID, errno, strerror(errno));
        }

    }

    return 0;
}

/** @return result
 *
 * 94 : Timeout (logon)
 */
int requestLogon(int sockfd, CLogonDbInfo & logonDbInfo)
{
    int ret;
    int nRecvLen;
    int nResult;
    char buff[SOCKET_BUFF];
    char logMsg[1024];       
    
	CKSSocket conn;
    CKSSocket customConn;
    
	memset(logMsg	,0x00	,sizeof(logMsg));

	customConn.attach(sockfd);

    ret = customConn.select();
    if( ret == 0 )
    {
        log_history(0,0,"[ERR] socket select - peer time out (%s)",strerror(errno));
        return -1;
    }

    if( ret < 0 )
    {
        log_history(0,0,"[ERR] socket select - peer wait error (%s)",strerror(errno));
        return -1;
    }

    memset(buff		,0x00	,SOCKET_BUFF);
    nRecvLen = customConn.recv(buff,SOCKET_BUFF);
    log_history(0,0,"[INF] logon bind request data rcv\n - [%s])",buff);
    if( nRecvLen == 0 ) {
        log_history(0,0,"[ERR] socket read failed - close by peer[%s]",strerror(errno));
        return -1;
    }
    if( nRecvLen < 0 ) {
        log_history(0,0,"[ERR] socket read failed - read error[%s]",strerror(errno));
        return -1;
    }
   // viewPack [info]
    viewPack(buff,nRecvLen);
    log_history(0,0,"[INF] attempting to connect to domain socket - [%s]", gConf.logonDBName);
    ret = conn.connectDomain(gConf.logonDBName);
    if( ret != 0 )
    {
		char *msg = new char[50];
		memset(msg,0x00,sizeof(msg));
		sprintf(msg,"[ERR] socket_domain connect failed - return[%d]",ret);
		log_history(0,0,msg,strerror(ret));
		return -1;
    }

    log_history(0,0,"[INF] domain socket connection successful - [%s]", gConf.logonDBName);
    ret = conn.send(buff,nRecvLen);
    log_history(0,0,"[INF] socket_domain logonDB send data - [%s]",buff);
    if( ret != nRecvLen )
    {
        log_history(0,0,"[ERR] socket_domain logonDB send failed - [%s]",strerror(errno));
        conn.close();
        return -1;
    }

	int e = errno;
	log_history(0, 0, "[DBG] client send ret[%d]/len[%d] errno[%d](%s)",
		ret, nRecvLen, e, strerror(e));

    memset(buff,0x00,SOCKET_BUFF);
    nRecvLen = conn.recv(buff,SOCKET_BUFF);
    log_history(0,0,"[INF] socket_domain logonDB read - [%s]",buff);
    if( nRecvLen == 0 ) 
	{
        log_history(0,0,"[ERR] socket_domain logonDB read failed - close by peer[%s]",strerror(errno));
        conn.close();
        return -1;
    }
    if( nRecvLen < 0 ) 
	{
        log_history(0,0,"[ERR] socket_domain logonDB read failed - read error[%s]",strerror(errno));
        conn.close();
        return -1;
    }
/*
    CLogonDbInfo* pRcvData = (CLogonDbInfo*)buff;
    TypeMsgBindAck Ack;
    memset(&Ack,0x00,sizeof(Ack));
    strcpy(Ack.header.msgType,"2");
    strcpy(Ack.header.msgLeng,"2");
    if( pRcvData->nmRST == 0 )
        memcpy(Ack.szResult,"00",2);
    else
        memcpy(Ack.szResult,"33", 2);
*/
    ret = customConn.send(buff,nRecvLen);
    log_history(0,0,"[INF] socket send bind ack - [%s]",buff);
    if( ret != nRecvLen )
    {
        log_history(0,0,"[ERR] socket send bind ack failed - read error[%s]",strerror(errno));
        conn.close();
        return -1;
    }

    ret = conn.send("OK",3);
    if( ret != 3 )
    {
        log_history(0,0,"[ERR] socket_domain logonDB send failed - [%s]",strerror(errno));
        conn.close();
        return -1;
    }

    memset(buff,0x00,SOCKET_BUFF);
    nRecvLen = conn.recv(buff,SOCKET_BUFF);
    log_history(0,0,"[INF] socket_domain logonDB read - config info (%s)",buff);
    if( nRecvLen == 0 ) 
	{
        log_history(0,0,"[ERR] socket_domain logonDB read failed - close by peer[%s]",strerror(errno));
        conn.close();
        return -1;
    }
    if( nRecvLen < 0 ) 
	{
        log_history(0,0,"[ERR] socket_domain logonDB read failed - read error[%s]",strerror(errno));
        conn.close();
        return -1;
    }

    conn.close();

    CLogonDbInfo* pRcvData = (CLogonDbInfo*)buff;
/*
		sprintf(logMsg,"[INF] rcv data\n - szCID[%s]\n- szPWD[%s]\n- szSIP[%s]\n- szAPPName[%s]\n- szServerInfo[%s]\n- nRptNoDataSleep[%d]\n- szSenderName[%s]\n- szReportName[%s]\n- szSenderDBName[%s]\n- szReportDBName[%s]\n- szLogFilePath[%s]\n- szReserve[%s]\n- nmPID[%d]\n- nmJOB[%d]\n- nmPRT[%d]\n- nmCNT[%d]\n- nmRST[%d]\n- nUrlJob[%d]\n- nRptWait[%d]\n- classify[%c]\n- szIP[%s]\n- szLogPath[%s]\n- szDomainPath[%s]\n- szLimitType[%s]\n- szLimitFlag[%s]\n- nDaywarnCnt[%d]\n- nMonWarnCnt[%d]\n- nDayLimitCnt[%d]\n- nMonLimitCnt[%d]\n- nDayAccCnt[%d]\n- nMonAccCnt[%d]\n- nCurAccCnt[%d]\n- szDate[%s]\n"
			,pRcvData->szCID
			,pRcvData->szPWD
			,pRcvData->szSIP
			,pRcvData->szAPPName
			,pRcvData->szServerInfo
			,pRcvData->nRptNoDataSleep
			,pRcvData->szSenderName
			,pRcvData->szReportName
			,pRcvData->szSenderDBName
			,pRcvData->szReportDBName
			,pRcvData->szLogFilePath
			,pRcvData->szReserve
			,pRcvData->nmPID
			,pRcvData->nmJOB
			,pRcvData->nmPRT
			,pRcvData->nmCNT
			,pRcvData->nmRST
			,pRcvData->nUrlJob
			,pRcvData->nRptWait
			,pRcvData->classify
			,pRcvData->szIP
			,pRcvData->szLogPath
			,pRcvData->szDomainPath
			,pRcvData->szLimitType
			,pRcvData->szLimitFlag
			,pRcvData->nDayWarnCnt
			,pRcvData->nMonWarnCnt
			,pRcvData->nDayLimitCnt
			,pRcvData->nMonLimitCnt
			,pRcvData->nDayAccCnt
			,pRcvData->nMonAccCnt
			,pRcvData->nCurAccCnt
			,pRcvData->szDate
			);
*/
        sprintf(logMsg,"[INF] rcv data\n - szCID[]\n- szPWD[]\n- szSIP[]\n- szAPPName[%s]\n- szServerInfo[%s]\n- nRptNoDataSleep[%d]\n- szSenderName[%s]\n- szReportName[%s]\n- szSenderDBName[%s]\n- szReportDBName[%s]\n- szLogFilePath[%s]\n- szReserve[%s]\n- nmPID[%d]\n- nmJOB[%d]\n- nmPRT[%d]\n- nmCNT[%d]\n- nmRST[%d]\n- nUrlJob[%d]\n- nRptWait[%d]\n- classify[%c]\n- szIP[%s]\n- szLogPath[%s]\n- szDomainPath[%s]\n- szLimitType[%s]\n- szLimitFlag[%s]\n- nDaywarnCnt[%d]\n- nMonWarnCnt[%d]\n- nDayLimitCnt[%d]\n- nMonLimitCnt[%d]\n- nDayAccCnt[%d]\n- nMonAccCnt[%d]\n- nCurAccCnt[%d]\n- szDate[%s]\n"
            ,pRcvData->szAPPName
            ,pRcvData->szServerInfo
			,pRcvData->nRptNoDataSleep
			,pRcvData->szSenderName
			,pRcvData->szReportName
			,pRcvData->szSenderDBName
			,pRcvData->szReportDBName
			,pRcvData->szLogFilePath
			,pRcvData->szReserve
			,pRcvData->nmPID
			,pRcvData->nmJOB
			,pRcvData->nmPRT
			,pRcvData->nmCNT
			,pRcvData->nmRST
			,pRcvData->nUrlJob
			,pRcvData->nRptWait
			,pRcvData->classify
			,pRcvData->szIP
			,pRcvData->szLogPath
			,pRcvData->szDomainPath
			,pRcvData->szLimitType
			,pRcvData->szLimitFlag
			,pRcvData->nDayWarnCnt
			,pRcvData->nMonWarnCnt
			,pRcvData->nDayLimitCnt
			,pRcvData->nMonLimitCnt
			,pRcvData->nDayAccCnt
			,pRcvData->nMonAccCnt
			,pRcvData->nCurAccCnt
			,pRcvData->szDate
			);



    monitoring(logMsg,0,0);

    if( pRcvData->nmRST == 0 )
    {
        memcpy(&logonDbInfo,pRcvData,sizeof(logonDbInfo));
        return 0;
    }
    else
		{
        return -1;
		}
}


/* @brief logonInfo에서 가져온 IP:PORT 를 정보와 일치하는지 체크
 * @return succ 0 fail -1
 */
int checkServerInfo(CLogonDbInfo& logonDbInfo,char* szIP,int nPort)
{
    char szCmpString[32];

    CCL(szCmpString);
    sprintf(szCmpString,"%s:%d",szIP,nPort);

    /* 값이 없으면 체크 하지 않는다 */
//    if( strlen(logonDbInfo.szServerInfo) != 0 )
//    {
//        if( strstr(logonDbInfo.szServerInfo,szCmpString) == 0 )
//        {
//            log_history(0,0,"[ERR] server ip config check failed - db[%s] file[%s]",
//                    logonDbInfo.szServerInfo,
//                    szCmpString);
//            return -1;
//        }
//    }


    /* 허용 아이피 체크 0.0.0.0 : 모두 허용 */
    if( strstr(logonDbInfo.szSIP,"0.0.0.0") == NULL )
    {
        if( strstr(logonDbInfo.szSIP,logonDbInfo.szIP) == NULL )
        {
            log_history(0,0,"[ERR] ip auth failed - config[%s] custom[%s]",
                    logonDbInfo.szSIP,
                    logonDbInfo.szIP);
            return -2;
        } 
				else 
				{
            log_history(0,0,"[INF] ip auth success #1 - config[%s] custom[%s]",
                    logonDbInfo.szSIP,
                    logonDbInfo.szIP);

        }
    }
   	else 
		{
            log_history(0,0,"[INF] ip auth success #2 - config[%s] custom[%s]",
                    logonDbInfo.szSIP,
                    logonDbInfo.szIP);

    }

    return 0;
}

int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.serverIP , conf.FetchEntry("gw.ip"),16);
    if( gConf.serverIP == NULL )
	{	
		strcpy(gConf.serverIP,"");
	}

    conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
    if( gConf.logonDBName == NULL )
	{	
		strcpy(gConf.logonDBName,"");
	}

    gConf.serverPORT = conf.FetchEntryInt("gw.port");
    gConf.process_sleep_time = conf.FetchEntryInt("process.sleeptime");


    conf.strncpy2(gConf.bindir , conf.FetchEntry("process.bindir"),64);
    if( gConf.bindir == NULL ) 
	{
		strcpy(gConf.bindir,"");
	}

    conf.strncpy2(gConf.cfgdir , conf.FetchEntry("process.cfgdir"),64);
    if( gConf.cfgdir == NULL ) 
	{
		strcpy(gConf.cfgdir,"");
	}

    conf.strncpy2(gConf.logPath , conf.FetchEntry("log.path"),64);
    if( gConf.logPath == NULL ) 
	{
		strcpy(gConf.logPath,"");
	}

    conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.path"),64);
    if( gConf.domainPath == NULL ) 
	{
		strcpy(gConf.domainPath,"");
	}

    conf.strncpy2(gConf.l4IP , conf.FetchEntry("gw.l4IP"),64);
    if( gConf.bindir == NULL ) 
	{
		strcpy(gConf.bindir,"");
	}

    return 0;
}

int checkLoginDup(CLogonDbInfo &logonDbInfo)
{
    return 0;
}

